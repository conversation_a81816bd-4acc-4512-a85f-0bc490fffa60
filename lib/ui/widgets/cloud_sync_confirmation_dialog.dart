import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:noeji/ui/theme/noeji_theme.dart';

/// Confirmation dialog for enabling cloud sync
class CloudSyncConfirmationDialog extends StatelessWidget {
  /// Callback when user confirms to enable cloud sync
  final VoidCallback onConfirm;
  
  /// Callback when user cancels
  final VoidCallback onCancel;

  /// Constructor
  const CloudSyncConfirmationDialog({
    super.key,
    required this.onConfirm,
    required this.onCancel,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: NoejiTheme.colorsOf(context).border,
          width: 1,
        ),
      ),
      title: Text(
        'Enable Cloud Data Sync',
        style: GoogleFonts.afacad(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: NoejiTheme.colorsOf(context).textPrimary,
        ),
      ),
      content: Text(
        'Are you sure to enable cloud data sync? All your data will be synced to cloud after you confirm. Once enabled, this option can\'t be turned off.',
        style: GoogleFonts.afacad(
          fontSize: 16,
          color: NoejiTheme.colorsOf(context).textPrimary,
        ),
      ),
      actions: [
        // Cancel button
        TextButton(
          onPressed: onCancel,
          child: Text(
            'Cancel',
            style: GoogleFonts.afacad(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: NoejiTheme.colorsOf(context).textSecondary,
            ),
          ),
        ),
        
        // Confirm button
        TextButton(
          onPressed: onConfirm,
          child: Text(
            'Enable',
            style: GoogleFonts.afacad(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: NoejiTheme.colorsOf(context).textPrimary,
            ),
          ),
        ),
      ],
    );
  }
}

/// Show the cloud sync confirmation dialog
Future<bool?> showCloudSyncConfirmationDialog(BuildContext context) {
  return showDialog<bool>(
    context: context,
    barrierDismissible: false,
    builder: (context) => CloudSyncConfirmationDialog(
      onConfirm: () => Navigator.of(context).pop(true),
      onCancel: () => Navigator.of(context).pop(false),
    ),
  );
}
