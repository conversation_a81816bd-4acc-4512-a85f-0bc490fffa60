import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:noeji/services/preferences/cloud_sync_storage.dart';
import 'package:noeji/services/firebase/firebase_init.dart';
import 'package:noeji/utils/logger.dart';

/// State class for cloud sync preferences
class CloudSyncState {
  final bool isEnabled;
  final bool isLoaded;
  final bool isPermanent; // Once enabled, cannot be disabled

  const CloudSyncState({
    this.isEnabled = false,
    this.isLoaded = false,
    this.isPermanent = false,
  });

  CloudSyncState copyWith({
    bool? isEnabled,
    bool? isLoaded,
    bool? isPermanent,
  }) {
    return CloudSyncState(
      isEnabled: isEnabled ?? this.isEnabled,
      isLoaded: isLoaded ?? this.isLoaded,
      isPermanent: isPermanent ?? this.isPermanent,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CloudSyncState &&
          runtimeType == other.runtimeType &&
          isEnabled == other.isEnabled &&
          isLoaded == other.isLoaded &&
          isPermanent == other.isPermanent;

  @override
  int get hashCode => isEnabled.hashCode ^ isLoaded.hashCode ^ isPermanent.hashCode;
}

/// Notifier for cloud sync preferences
class CloudSyncNotifier extends StateNotifier<CloudSyncState> {
  CloudSyncNotifier() : super(const CloudSyncState()) {
    _loadSavedPreferences();
  }

  /// Load the saved preferences from storage
  Future<void> _loadSavedPreferences() async {
    try {
      final isEnabled = await CloudSyncStorage.loadCloudSyncEnabled();
      
      // Update the state
      state = state.copyWith(
        isEnabled: isEnabled,
        isLoaded: true,
        isPermanent: isEnabled, // If enabled, it's permanent
      );

      Logger.debug('Cloud sync preferences loaded: isEnabled=$isEnabled');
    } catch (e) {
      Logger.error('Error loading cloud sync preferences', e);
      // Set loaded to true even on error so UI doesn't wait indefinitely
      state = state.copyWith(isLoaded: true);
    }
  }

  /// Enable cloud sync (permanent action)
  Future<bool> enableCloudSync() async {
    try {
      // Enable Firestore network access first
      await enableFirestoreNetworkAccess();
      
      // Save to storage
      final success = await CloudSyncStorage.saveCloudSyncEnabled(true);

      if (success) {
        // Update the state - once enabled, it becomes permanent
        state = state.copyWith(
          isEnabled: true,
          isPermanent: true,
        );
        Logger.debug('Cloud sync enabled successfully');
        return true;
      } else {
        Logger.error('Failed to save cloud sync enabled preference');
        return false;
      }
    } catch (e) {
      Logger.error('Error enabling cloud sync', e);
      return false;
    }
  }

  /// Reset cloud sync preference for free users (called when user becomes free)
  Future<void> resetForFreeUser() async {
    try {
      // Reset the preference in storage
      await CloudSyncStorage.resetCloudSyncEnabled();
      
      // Update the state
      state = state.copyWith(
        isEnabled: false,
        isPermanent: false,
      );
      
      Logger.debug('Cloud sync preference reset for free user');
    } catch (e) {
      Logger.error('Error resetting cloud sync preference for free user', e);
    }
  }
}

/// Provider for cloud sync preferences
final cloudSyncProvider = StateNotifierProvider<CloudSyncNotifier, CloudSyncState>((ref) {
  return CloudSyncNotifier();
});

/// Convenience provider for just the cloud sync enabled setting
final cloudSyncEnabledProvider = Provider<bool>((ref) {
  final cloudSync = ref.watch(cloudSyncProvider);
  return cloudSync.isEnabled;
});

/// Convenience provider for checking if cloud sync is loaded
final cloudSyncLoadedProvider = Provider<bool>((ref) {
  final cloudSync = ref.watch(cloudSyncProvider);
  return cloudSync.isLoaded;
});

/// Convenience provider for checking if cloud sync is permanent
final cloudSyncPermanentProvider = Provider<bool>((ref) {
  final cloudSync = ref.watch(cloudSyncProvider);
  return cloudSync.isPermanent;
});
