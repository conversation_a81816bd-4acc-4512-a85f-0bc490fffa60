import 'package:shared_preferences/shared_preferences.dart';
import 'package:noeji/utils/logger.dart';

/// Service for storing and retrieving cloud sync preferences
class CloudSyncStorage {
  static const String _cloudSyncEnabledKey = 'noeji_cloud_sync_enabled';

  /// Save the cloud sync enabled preference to persistent storage
  static Future<bool> saveCloudSyncEnabled(bool enabled) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final result = await prefs.setBool(_cloudSyncEnabledKey, enabled);

      if (result) {
        Logger.debug('Cloud sync enabled preference saved successfully: $enabled');
      } else {
        Logger.error('Failed to save cloud sync enabled preference');
      }

      return result;
    } catch (e) {
      Logger.error('Error saving cloud sync enabled preference', e);
      return false;
    }
  }

  /// Load the cloud sync enabled preference from persistent storage
  /// Returns false by default (cloud sync disabled for free users)
  static Future<bool> loadCloudSyncEnabled({bool defaultValue = false}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final value = prefs.getBool(_cloudSyncEnabledKey);

      if (value == null) {
        Logger.debug(
          'No cloud sync enabled preference found, using default: $defaultValue',
        );
        return defaultValue;
      }

      Logger.debug(
        'Cloud sync enabled preference loaded successfully: $value',
      );
      return value;
    } catch (e) {
      Logger.error('Error loading cloud sync enabled preference', e);
      return defaultValue;
    }
  }

  /// Reset the cloud sync enabled preference (set to false)
  static Future<bool> resetCloudSyncEnabled() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final result = await prefs.setBool(_cloudSyncEnabledKey, false);

      if (result) {
        Logger.debug('Cloud sync enabled preference reset successfully');
      } else {
        Logger.error('Failed to reset cloud sync enabled preference');
      }

      return result;
    } catch (e) {
      Logger.error('Error resetting cloud sync enabled preference', e);
      return false;
    }
  }
}
